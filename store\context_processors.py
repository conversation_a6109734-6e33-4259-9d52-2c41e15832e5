from .models import <PERSON><PERSON>


def cart_context(request):
    """
    Context processor to add cart information to all templates
    """
    cart_items_count = 0
    cart_total = 0
    
    if request.user.is_authenticated:
        try:
            cart = Cart.objects.get(user=request.user)
            cart_items_count = cart.total_items
            cart_total = cart.total_price
        except Cart.DoesNotExist:
            pass
    
    return {
        'cart_items_count': cart_items_count,
        'cart_total': cart_total,
    }

from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils.text import slugify
from decimal import Decimal


class Category(models.Model):
    name = models.CharField(max_length=200, verbose_name='اسم الفئة')
    slug = models.SlugField(max_length=200, unique=True, verbose_name='الرابط')
    description = models.TextField(blank=True, verbose_name='الوصف')
    image = models.ImageField(upload_to='categories/', blank=True, verbose_name='الصورة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'فئة'
        verbose_name_plural = 'الفئات'
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('store:category_detail', args=[self.slug])


class Product(models.Model):
    AVAILABILITY_CHOICES = [
        ('in_stock', 'متوفر'),
        ('out_of_stock', 'غير متوفر'),
        ('limited', 'كمية محدودة'),
    ]

    name = models.CharField(max_length=200, verbose_name='اسم المنتج')
    slug = models.SlugField(max_length=200, unique=True, verbose_name='الرابط')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products', verbose_name='الفئة')
    description = models.TextField(verbose_name='الوصف')
    short_description = models.CharField(max_length=300, verbose_name='وصف مختصر')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر')
    discount_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name='سعر الخصم')
    image = models.ImageField(upload_to='products/', verbose_name='الصورة الرئيسية')
    stock_quantity = models.PositiveIntegerField(default=0, verbose_name='الكمية المتوفرة')
    availability = models.CharField(max_length=20, choices=AVAILABILITY_CHOICES, default='in_stock', verbose_name='حالة التوفر')
    is_featured = models.BooleanField(default=False, verbose_name='منتج مميز')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'منتج'
        verbose_name_plural = 'المنتجات'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('store:product_detail', args=[self.slug])

    @property
    def get_price(self):
        if self.discount_price:
            return self.discount_price
        return self.price

    @property
    def get_discount_percentage(self):
        if self.discount_price and self.price:
            return int(((self.price - self.discount_price) / self.price) * 100)
        return 0

    @property
    def is_in_stock(self):
        return self.stock_quantity > 0 and self.availability == 'in_stock'


class ProductImage(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images', verbose_name='المنتج')
    image = models.ImageField(upload_to='products/gallery/', verbose_name='الصورة')
    alt_text = models.CharField(max_length=200, blank=True, verbose_name='النص البديل')
    is_main = models.BooleanField(default=False, verbose_name='الصورة الرئيسية')

    class Meta:
        verbose_name = 'صورة المنتج'
        verbose_name_plural = 'صور المنتجات'

    def __str__(self):
        return f'{self.product.name} - صورة'


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    phone = models.CharField(max_length=20, blank=True, verbose_name='رقم الهاتف')
    birth_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')
    avatar = models.ImageField(upload_to='avatars/', blank=True, verbose_name='الصورة الشخصية')

    class Meta:
        verbose_name = 'الملف الشخصي'
        verbose_name_plural = 'الملفات الشخصية'

    def __str__(self):
        return f'{self.user.username} - الملف الشخصي'


class Address(models.Model):
    GOVERNORATE_CHOICES = [
        ('cairo', 'القاهرة'),
        ('giza', 'الجيزة'),
        ('alexandria', 'الإسكندرية'),
        ('qalyubia', 'القليوبية'),
        ('port_said', 'بورسعيد'),
        ('suez', 'السويس'),
        ('luxor', 'الأقصر'),
        ('aswan', 'أسوان'),
        ('asyut', 'أسيوط'),
        ('beheira', 'البحيرة'),
        ('beni_suef', 'بني سويف'),
        ('dakahlia', 'الدقهلية'),
        ('damietta', 'دمياط'),
        ('fayyum', 'الفيوم'),
        ('gharbia', 'الغربية'),
        ('ismailia', 'الإسماعيلية'),
        ('kafr_el_sheikh', 'كفر الشيخ'),
        ('matrouh', 'مطروح'),
        ('minya', 'المنيا'),
        ('monufia', 'المنوفية'),
        ('new_valley', 'الوادي الجديد'),
        ('north_sinai', 'شمال سيناء'),
        ('qena', 'قنا'),
        ('red_sea', 'البحر الأحمر'),
        ('sharqia', 'الشرقية'),
        ('sohag', 'سوهاج'),
        ('south_sinai', 'جنوب سيناء'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='addresses', verbose_name='المستخدم')
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    governorate = models.CharField(max_length=50, choices=GOVERNORATE_CHOICES, verbose_name='المحافظة')
    city = models.CharField(max_length=100, verbose_name='المدينة')
    street_address = models.CharField(max_length=200, verbose_name='عنوان الشارع')
    building_number = models.CharField(max_length=20, verbose_name='رقم المبنى')
    floor_number = models.CharField(max_length=10, blank=True, verbose_name='رقم الطابق')
    apartment_number = models.CharField(max_length=10, blank=True, verbose_name='رقم الشقة')
    postal_code = models.CharField(max_length=10, blank=True, verbose_name='الرمز البريدي')
    is_default = models.BooleanField(default=False, verbose_name='العنوان الافتراضي')

    class Meta:
        verbose_name = 'عنوان'
        verbose_name_plural = 'العناوين'

    def __str__(self):
        return f'{self.full_name} - {self.governorate}'


class Cart(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'سلة التسوق'
        verbose_name_plural = 'سلال التسوق'

    def __str__(self):
        return f'سلة {self.user.username}'

    @property
    def total_price(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def total_items(self):
        return sum(item.quantity for item in self.items.all())


class CartItem(models.Model):
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items', verbose_name='السلة')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='المنتج')
    quantity = models.PositiveIntegerField(default=1, verbose_name='الكمية')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')

    class Meta:
        verbose_name = 'عنصر السلة'
        verbose_name_plural = 'عناصر السلة'
        unique_together = ('cart', 'product')

    def __str__(self):
        return f'{self.product.name} - {self.quantity}'

    @property
    def total_price(self):
        return self.product.get_price * self.quantity


class Order(models.Model):
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('confirmed', 'مؤكد'),
        ('processing', 'قيد التجهيز'),
        ('shipped', 'تم الشحن'),
        ('delivered', 'تم التسليم'),
        ('cancelled', 'ملغي'),
        ('returned', 'مرتجع'),
    ]

    PAYMENT_CHOICES = [
        ('cash_on_delivery', 'الدفع عند الاستلام'),
        ('bank_transfer', 'تحويل بنكي'),
        ('credit_card', 'بطاقة ائتمان'),
        ('mobile_wallet', 'محفظة إلكترونية'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders', verbose_name='المستخدم')
    order_number = models.CharField(max_length=20, unique=True, verbose_name='رقم الطلب')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='حالة الطلب')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_CHOICES, verbose_name='طريقة الدفع')

    # Shipping Address
    shipping_full_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    shipping_phone = models.CharField(max_length=20, verbose_name='هاتف المستلم')
    shipping_governorate = models.CharField(max_length=50, verbose_name='المحافظة')
    shipping_city = models.CharField(max_length=100, verbose_name='المدينة')
    shipping_address = models.CharField(max_length=200, verbose_name='العنوان')

    # Pricing
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المجموع الفرعي')
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='تكلفة الشحن')
    total = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المجموع الكلي')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # Notes
    notes = models.TextField(blank=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'طلب'
        verbose_name_plural = 'الطلبات'
        ordering = ['-created_at']

    def __str__(self):
        return f'طلب رقم {self.order_number}'

    def save(self, *args, **kwargs):
        if not self.order_number:
            import uuid
            self.order_number = str(uuid.uuid4())[:8].upper()
        super().save(*args, **kwargs)


class OrderItem(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items', verbose_name='الطلب')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='المنتج')
    quantity = models.PositiveIntegerField(verbose_name='الكمية')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='السعر')

    class Meta:
        verbose_name = 'عنصر الطلب'
        verbose_name_plural = 'عناصر الطلب'

    def __str__(self):
        return f'{self.product.name} - {self.quantity}'

    @property
    def total_price(self):
        return self.price * self.quantity


class Review(models.Model):
    RATING_CHOICES = [
        (1, '⭐'),
        (2, '⭐⭐'),
        (3, '⭐⭐⭐'),
        (4, '⭐⭐⭐⭐'),
        (5, '⭐⭐⭐⭐⭐'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews', verbose_name='المنتج')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    rating = models.IntegerField(choices=RATING_CHOICES, verbose_name='التقييم')
    comment = models.TextField(verbose_name='التعليق')
    is_approved = models.BooleanField(default=True, verbose_name='موافق عليه')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        verbose_name = 'مراجعة'
        verbose_name_plural = 'المراجعات'
        unique_together = ('product', 'user')
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.product.name} - {self.user.username} - {self.rating} نجوم'


class Wishlist(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='المستخدم')
    products = models.ManyToManyField(Product, blank=True, verbose_name='المنتجات')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        verbose_name = 'قائمة الأمنيات'
        verbose_name_plural = 'قوائم الأمنيات'

    def __str__(self):
        return f'قائمة أمنيات {self.user.username}'
